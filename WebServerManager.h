#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include "WiFiManager.h"
#include "DeviceManager.h"
#include "MQTTManager.h"

class WebServerManager
{
private:
    ESP8266WebServer _server;
    WiFiManager *_wifiManager;
    DeviceManager *_deviceManager;
    MQTTManager *_mqttManager;
    bool _serverRunning;
    int _port;

    // HTML templates
    const char *_htmlHeader = R"(<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP8266 Switch Control</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        .navbar {
            background-color: #2d2d2d;
            padding: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            border-bottom: 2px solid #666;
        }
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
        }
        .nav-brand {
            padding: 15px 20px;
            font-size: 1.2em;
            font-weight: bold;
            color: #ccc;
            text-decoration: none;
        }
        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            flex: 1;
        }
        .nav-item {
            margin: 0;
        }
        .nav-link {
            display: block;
            padding: 15px 20px;
            color: #e0e0e0;
            text-decoration: none;
            transition: background-color 0.3s, color 0.3s;
            border-radius: 0;
            cursor: pointer;
        }
        .nav-link:hover {
            background-color: #3d3d3d;
            color: #fff;
        }
        .nav-link.active {
            background-color: #555;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .tab-content {
            background-color: #2d2d2d;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        h1 {
            color: #ccc;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2em;
        }
        h2 {
            color: #bbb;
            border-bottom: 2px solid #666;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-item {
            background-color: #3d3d3d;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #666;
        }
        .info-label {
            font-weight: bold;
            color: #bbb;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        .info-value {
            color: #e0e0e0;
            font-size: 1.1em;
        }
        .switch-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .switch-status.on {
            background-color: #666;
            color: white;
        }
        .switch-status.off {
            background-color: #444;
            color: white;
        }
        .section-header {
            font-size: 1.2em;
            font-weight: bold;
            margin: 25px 0 15px 0;
            color: #bbb;
            border-bottom: 1px solid #555;
            padding-bottom: 8px;
        }
        .network-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        .network-item {
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid #555;
            border-radius: 12px;
            cursor: pointer;
            background-color: #3d3d3d;
            transition: all 0.3s;
        }
        .network-item:hover {
            background-color: #4a4a4a;
            transform: translateY(-2px);
        }
        .network-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .signal-strength {
            float: right;
            color: #bbb;
            font-size: 0.9em;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            background-color: #666;
            color: white;
            transition: all 0.3s;
        }
        button:hover {
            background-color: #777;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="nav-brand">ESP8266 Switch</a>
            <ul class="nav-menu">
                <li class="nav-item"><a href="#" class="nav-link active" id="dashboard-nav" onclick="showTab('dashboard')">Dashboard</a></li>
                <li class="nav-item"><a href="#" class="nav-link" id="wifi-nav" onclick="showTab('wifi')">WiFi</a></li>
                <li class="nav-item"><a href="#" class="nav-link" id="switches-nav" onclick="showTab('switches')">Switch Control</a></li>
                <li class="nav-item"><a href="#" class="nav-link" id="mqtt-nav" onclick="showTab('mqtt')">MQTT</a></li>
            </ul>
        </div>
    </nav>
    <div class="container">)";

    const char *_htmlFooter = R"(    </div>
    <script>
        function showTab(tabName) {
            var tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(function(content) {
                content.classList.remove('active');
            });
            var navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(function(link) {
                link.classList.remove('active');
            });
            var selectedTab = document.getElementById(tabName + '-tab');
            if (selectedTab) {
                selectedTab.classList.add('active');
            }
            var selectedNav = document.getElementById(tabName + '-nav');
            if (selectedNav) {
                selectedNav.classList.add('active');
            }
        }
        function toggleSwitch(index) {
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/switch', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    location.reload();
                }
            };
            xhr.send('index=' + index + '&state=' + (Math.random() > 0.5 ? '1' : '0'));
        }
        document.addEventListener('DOMContentLoaded', function() {
            showTab('dashboard');
        });
    </script>
</body>
</html>)";

public:
    // Constructor that accepts TouchSensorManager (for compatibility with existing code)
    WebServerManager(WiFiManager *wifiManager, DeviceManager *deviceManager = nullptr, void *touchManager = nullptr, int port = 80)
        : _server(port), _wifiManager(wifiManager), _deviceManager(deviceManager), _mqttManager(nullptr), _serverRunning(false), _port(port)
    {
        // touchManager parameter is ignored for now - kept for compatibility
    }

    // Alternative constructor with MQTTManager
    WebServerManager(WiFiManager *wifiManager, DeviceManager *deviceManager, MQTTManager *mqttManager, int port = 80)
        : _server(port), _wifiManager(wifiManager), _deviceManager(deviceManager), _mqttManager(mqttManager), _serverRunning(false), _port(port)
    {
    }

    void begin()
    {
        // Main page
        _server.on("/", [this]()
                   { handleRoot(); });

        // WiFi routes
        _server.on("/wifi", [this]()
                   { handleWifi(); });
        _server.on("/connect", [this]()
                   { handleConnect(); });
        _server.on("/hidden", [this]()
                   { handleHiddenNetwork(); });
        _server.on("/submit-hidden", [this]()
                   { handleSubmitHidden(); });
        _server.on("/success", [this]()
                   { handleSuccess(); });

        // Device control routes
        _server.on("/device", [this]()
                   { handleDevice(); });
        _server.on("/switch", [this]()
                   { handleSwitch(); });
        _server.on("/rgb/off", [this]()
                   { handleRGBOff(); });
        _server.on("/rgb/on", [this]()
                   { handleRGBOn(); });

        // 404 handler
        _server.onNotFound([this]()
                           { handle404(); });

        _server.begin();
        _serverRunning = true;
        Serial.println("Web server started");
        Serial.print("Web server running on port ");
        Serial.println(_port);
    }

    void handleClient()
    {
        if (_serverRunning)
        {
            _server.handleClient();
        }
    }

    bool isRunning() { return _serverRunning; }

    void stop()
    {
        _server.stop();
        _serverRunning = false;
        Serial.println("Web server stopped");
    }

private:
    void handleRoot()
    {
        String html = _htmlHeader;

        // Dashboard Tab
        html += "<div id='dashboard-tab' class='tab-content active'>";
        html += "<h1>Dashboard</h1>";

        // WiFi Status
        html += "<h2>WiFi Connection</h2>";
        html += "<div class='info-grid'>";
        html += "<div class='info-item'>";
        html += "<div class='info-label'>Status:</div>";
        html += "<div class='info-value'>" + String(_wifiManager->isConnected() ? "Connected" : "Disconnected") + "</div>";
        html += "</div>";

        if (_wifiManager->isConnected())
        {
            String currentSSID = WiFi.SSID();
            if (currentSSID.length() == 0)
            {
                currentSSID = _wifiManager->getSSID();
            }
            IPAddress ip = WiFi.localIP();

            html += "<div class='info-item'>";
            html += "<div class='info-label'>Network:</div>";
            html += "<div class='info-value'>" + currentSSID + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>IP Address:</div>";
            html += "<div class='info-value'>" + ip.toString() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Signal Strength:</div>";
            html += "<div class='info-value'>" + String(WiFi.RSSI()) + " dBm</div>";
            html += "</div>";
        }
        html += "</div>";

        // Device Information
        if (_deviceManager)
        {
            html += "<h2>Device Information</h2>";
            html += "<div class='info-grid'>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device ID:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceID() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device Name:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceName() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device Type:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceType() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Switch Count:</div>";
            html += "<div class='info-value'>" + String(_deviceManager->getSwitchCount()) + "</div>";
            html += "</div>";
            html += "</div>";

            // Switch Status Overview
            html += "<h2>Switch Status</h2>";
            html += "<div class='info-grid'>";
            for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
            {
                bool state = _deviceManager->getSwitchState(i);
                html += "<div class='info-item'>";
                html += "<div class='info-label'>Switch " + String(i + 1) + ":</div>";
                html += "<div class='info-value'>";
                html += "<span class='switch-status " + String(state ? "on" : "off") + "'>" + String(state ? "ON" : "OFF") + "</span>";
                html += "</div>";
                html += "</div>";
            }
            html += "</div>";
        }
        html += "</div>";

        // WiFi Tab
        html += "<div id='wifi-tab' class='tab-content'>";
        html += "<h1>WiFi Configuration</h1>";

        // Current network
        if (_wifiManager->isConnected())
        {
            String currentSSID = WiFi.SSID();
            if (currentSSID.length() == 0)
            {
                currentSSID = _wifiManager->getSSID();
            }

            html += "<div class='section-header'>Current Network</div>";
            html += "<div class='network-item'>";
            html += "<div class='network-name'>" + currentSSID + "</div>";
            html += "<span class='signal-strength'>" + String(WiFi.RSSI()) + " dBm</span>";
            html += "</div>";
        }

        // Available networks
        html += "<div class='section-header'>Available Networks</div>";
        html += "<ul class='network-list'>";

        int n = WiFi.scanNetworks();
        if (n == 0)
        {
            html += "<div class='network-item'>";
            html += "<div class='network-name'>No networks found</div>";
            html += "</div>";
        }
        else
        {
            for (int i = 0; i < n; ++i)
            {
                String ssid = WiFi.SSID(i);
                int32_t rssi = WiFi.RSSI(i);
                String encType = (WiFi.encryptionType(i) == ENC_TYPE_NONE) ? "Open" : "Secured";

                html += "<div class='network-item' onclick='window.location=\"/connect?ssid=" + ssid + "\"'>";
                html += "<div class='network-name'>" + ssid + "</div>";
                html += "<span class='signal-strength'>" + String(rssi) + " dBm (" + encType + ")</span>";
                html += "</div>";
            }
        }
        html += "</ul>";

        html += "<div class='section-header'>Manual Connection</div>";
        html += "<div class='network-item' onclick='window.location=\"/hidden\"'>";
        html += "<div class='network-name'>+ Add Hidden Network</div>";
        html += "</div>";
        html += "</div>";

        // Switch Control Tab
        html += "<div id='switches-tab' class='tab-content'>";
        html += "<h1>Switch Control</h1>";

        if (_deviceManager)
        {
            for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
            {
                bool state = _deviceManager->getSwitchState(i);

                html += "<div class='info-item'>";
                html += "<div class='info-label'>Switch " + String(i + 1) + ":</div>";
                html += "<div class='info-value'>";
                html += "<span class='switch-status " + String(state ? "on" : "off") + "'>" + String(state ? "ON" : "OFF") + "</span>";
                html += "<button onclick='toggleSwitch(" + String(i) + ")' style='margin-left: 10px;'>Toggle</button>";
                html += "</div>";
                html += "</div>";
            }
        }
        else
        {
            html += "<p>Device manager not available.</p>";
        }
        html += "</div>";

        // MQTT Tab
        html += "<div id='mqtt-tab' class='tab-content'>";
        html += "<h1>MQTT Status</h1>";

        html += "<h2>Connection Status</h2>";
        html += "<div class='info-grid'>";
        html += "<div class='info-item'>";
        html += "<div class='info-label'>Status:</div>";
        if (_mqttManager)
        {
            html += "<div class='info-value'>" + String(_mqttManager->isConnected() ? "Connected" : "Disconnected") + "</div>";
        }
        else
        {
            html += "<div class='info-value'>Information not available</div>";
        }
        html += "</div>";
        html += "</div>";

        if (_deviceManager)
        {
            html += "<h2>MQTT Topics</h2>";
            html += "<div class='info-grid'>";

            String deviceId = _deviceManager->getDeviceID();

            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device ID:</div>";
            html += "<div class='info-value'>" + deviceId + "</div>";
            html += "</div>";

            html += "<div class='info-item'>";
            html += "<div class='info-label'>Status Topic:</div>";
            html += "<div class='info-value'>home/switches/" + deviceId + "/available</div>";
            html += "</div>";
            html += "</div>";
        }
        html += "</div>";

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle WiFi configuration page
    void handleWifi()
    {
        _server.sendHeader("Location", "/#wifi", true);
        _server.send(302, "text/plain", "");
    }

    // Handle connect page
    void handleConnect()
    {
        String ssid = _server.arg("ssid");
        String html = _htmlHeader;
        html += "<div id='dashboard-tab' class='tab-content active'>";
        html += "<h1>Connect to Network</h1>";
        html += "<h2>" + ssid + "</h2>";
        html += "<form method='post' action='/submit-hidden'>";
        html += "<input type='hidden' name='ssid' value='" + ssid + "'>";
        html += "<label for='password'>Network Password:</label>";
        html += "<input type='password' id='password' name='password' required>";
        html += "<button type='submit'>Connect</button>";
        html += "</form>";
        html += "</div>";
        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle hidden network page
    void handleHiddenNetwork()
    {
        String html = _htmlHeader;
        html += "<div id='dashboard-tab' class='tab-content active'>";
        html += "<h1>Hidden Network</h1>";
        html += "<form method='post' action='/submit-hidden'>";
        html += "<label for='ssid'>Network Name (SSID):</label>";
        html += "<input type='text' id='ssid' name='ssid' required>";
        html += "<label for='password'>Network Password:</label>";
        html += "<input type='password' id='password' name='password' required>";
        html += "<button type='submit'>Connect</button>";
        html += "</form>";
        html += "</div>";
        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle WiFi connection submission
    void handleSubmitHidden()
    {
        String ssid = _server.arg("ssid");
        String password = _server.arg("password");

        if (ssid.length() == 0)
        {
            _server.send(400, "text/plain", "SSID is required");
            return;
        }

        bool connected = _wifiManager->connect(ssid, password);
        if (connected)
        {
            _wifiManager->saveCredentials(ssid, password);
            _server.sendHeader("Location", "/", true);
            _server.send(302, "text/plain", "");
        }
        else
        {
            _server.sendHeader("Location", "/success", true);
            _server.send(302, "text/plain", "");
        }
    }

    // Handle success page
    void handleSuccess()
    {
        String html = _htmlHeader;
        html += "<div id='dashboard-tab' class='tab-content active'>";
        html += "<h1>Connection Status</h1>";
        html += "<p>Connection failed. Please try again.</p>";
        html += "<button onclick='window.location=\"/\"'>Back to Home</button>";
        html += "</div>";
        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle device control page
    void handleDevice()
    {
        _server.sendHeader("Location", "/#switches", true);
        _server.send(302, "text/plain", "");
    }

    // Handle switch control
    void handleSwitch()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("state"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool state = _server.arg("state") == "1";

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setSwitchState(index, state);
        _server.send(200, "text/plain", "OK");
    }

    // Handle RGB OFF color control
    void handleRGBOff()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("r") || !_server.hasArg("g") || !_server.hasArg("b"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool r = _server.arg("r") == "1";
        bool g = _server.arg("g") == "1";
        bool b = _server.arg("b") == "1";

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setRGBOff(index, r, g, b);
        _server.send(200, "text/plain", "RGB OFF color set");
    }

    // Handle RGB ON color control
    void handleRGBOn()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("r") || !_server.hasArg("g") || !_server.hasArg("b"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool r = _server.arg("r") == "1";
        bool g = _server.arg("g") == "1";
        bool b = _server.arg("b") == "1";

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setRGBOn(index, r, g, b);
        _server.send(200, "text/plain", "RGB ON color set");
    }

    // Handle 404 errors
    void handle404()
    {
        String html = _htmlHeader;
        html += "<div id='dashboard-tab' class='tab-content active'>";
        html += "<h1>Page Not Found</h1>";
        html += "<p>The requested page was not found.</p>";
        html += "<button onclick='window.location=\"/\"'>Back to Home</button>";
        html += "</div>";
        html += _htmlFooter;
        _server.send(404, "text/html", html);
    }
};

#endif // WEB_SERVER_MANAGER_H
