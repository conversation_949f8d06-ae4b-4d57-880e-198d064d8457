#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include "WiFiManager.h"
#include "DeviceManager.h"
#include "MQTTManager.h"

class WebServerManager
{
private:
    ESP8266WebServer _server;
    WiFiManager *_wifiManager;
    DeviceManager *_deviceManager;
    MQTTManager *_mqttManager;
    bool _serverRunning;
    int _port;

    // HTML templates
    const char *_htmlHeader = R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP8266 Switch Control</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        
        /* Navigation Bar */
        .navbar {
            background-color: #2d2d2d;
            padding: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            border-bottom: 2px solid #666;
        }
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
        }
        .nav-brand {
            padding: 15px 20px;
            font-size: 1.2em;
            font-weight: bold;
            color: #ccc;
            text-decoration: none;
        }
        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            flex: 1;
        }
        .nav-item {
            margin: 0;
        }
        .nav-link {
            display: block;
            padding: 15px 20px;
            color: #e0e0e0;
            text-decoration: none;
            transition: background-color 0.3s, color 0.3s;
            border-radius: 0;
            cursor: pointer;
        }
        .nav-link:hover {
            background-color: #3d3d3d;
            color: #fff;
        }
        .nav-link.active {
            background-color: #555;
            color: white;
        }
        
        /* Main Content */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .tab-content {
            background-color: #2d2d2d;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        
        h1 {
            color: #ccc;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2em;
        }
        h2 {
            color: #bbb;
            border-bottom: 2px solid #666;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        h3 {
            color: #bbb;
            margin-bottom: 15px;
        }
        
        /* Info Grid */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-item {
            background-color: #3d3d3d;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #666;
        }
        .info-label {
            font-weight: bold;
            color: #bbb;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        .info-value {
            color: #e0e0e0;
            font-size: 1.1em;
        }
        
        /* Switch Controls */
        .switch-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .switch-card {
            background-color: #3d3d3d;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #555;
        }
        .switch-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .switch-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #bbb;
        }
        .switch-state {
            font-weight: bold;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .switch-state.on {
            background-color: #666;
            color: white;
        }
        .switch-state.off {
            background-color: #444;
            color: white;
        }
        .switch-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .switch-status.on {
            background-color: #666;
            color: white;
        }
        .switch-status.off {
            background-color: #444;
            color: white;
        }
        
        /* Toggle Switch */
        .toggle-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background-color: #555;
            border-radius: 15px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .toggle-switch.on {
            background-color: #777;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            background-color: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        .toggle-switch.on::after {
            transform: translateX(30px);
        }

        /* Color Controls */
        .color-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .color-group {
            background-color: #4a4a4a;
            padding: 20px;
            border-radius: 12px;
        }
        .color-group h4 {
            margin: 0 0 15px 0;
            color: #bbb;
            font-size: 1em;
        }
        .color-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .color-dropdown {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #666;
            border-radius: 8px;
            background-color: #3d3d3d;
            color: #e0e0e0;
            font-size: 14px;
        }
        .color-preview {
            width: 30px;
            height: 30px;
            border: 2px solid #666;
            border-radius: 8px;
            background-color: #000;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #bbb;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #555;
            border-radius: 8px;
            background-color: #3d3d3d;
            color: #e0e0e0;
            font-size: 16px;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #777;
            box-shadow: 0 0 5px rgba(119, 119, 119, 0.3);
        }
        .password-toggle {
            display: flex;
            align-items: center;
            margin-top: 8px;
        }
        .password-toggle label {
            margin-left: 8px;
            margin-bottom: 0;
            cursor: pointer;
        }

        /* Buttons */
        .button-container {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        button, .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #666;
            color: white;
        }
        .btn-primary:hover {
            background-color: #777;
        }
        .btn-secondary {
            background-color: #555;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #666;
        }
        .btn-warning {
            background-color: #888;
            color: white;
        }
        .btn-warning:hover {
            background-color: #999;
        }
        .btn-back {
            background-color: #444;
            color: white;
        }
        .btn-back:hover {
            background-color: #555;
        }

        /* WiFi Specific */
        .network-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        .network-item {
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid #555;
            border-radius: 12px;
            cursor: pointer;
            background-color: #3d3d3d;
            transition: all 0.3s;
        }
        .network-item:hover {
            background-color: #4a4a4a;
            transform: translateY(-2px);
        }
        .signal-strength {
            float: right;
            color: #bbb;
            font-size: 0.9em;
        }
        .scan-item {
            text-align: center;
            font-weight: bold;
            color: #777;
        }
        .scan-item:hover {
            background-color: #666;
            color: white;
        }
        .hidden-network-item {
            text-align: center;
            font-weight: bold;
            color: #888;
        }
        .hidden-network-item:hover {
            background-color: #777;
            color: white;
        }
        .section-header {
            font-size: 1.2em;
            font-weight: bold;
            margin: 25px 0 15px 0;
            color: #bbb;
            border-bottom: 1px solid #555;
            padding-bottom: 8px;
        }
        .network-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .current-network {
            background-color: #1a2e1a;
            border-left: 4px solid #666;
        }
        .plus-sign {
            color: #777;
            font-weight: bold;
            font-size: 1.2em;
            margin-right: 8px;
        }

        /* Status Messages */
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        .status.success {
            background-color: #666;
            color: white;
        }
        .status.error {
            background-color: #555;
            color: white;
        }
        .status.warning {
            background-color: #777;
            color: white;
        }

        /* Loading Spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            vertical-align: middle;
            margin-left: 8px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-menu {
                flex-direction: column;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: #2d2d2d;
                display: none;
            }
            .nav-menu.active {
                display: flex;
            }
            .container {
                padding: 10px;
            }
            .info-grid {
                grid-template-columns: 1fr;
            }
            .switch-grid {
                grid-template-columns: 1fr;
            }
            .color-controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="nav-brand">ESP8266 Switch</a>
            <ul class="nav-menu">
                <li class="nav-item"><a href="#" class="nav-link active" id="dashboard-nav" onclick="showTab('dashboard')">Dashboard</a></li>
                <li class="nav-item"><a href="#" class="nav-link" id="wifi-nav" onclick="showTab('wifi')">WiFi</a></li>
                <li class="nav-item"><a href="#" class="nav-link" id="switches-nav" onclick="showTab('switches')">Switch Control</a></li>
                <li class="nav-item"><a href="#" class="nav-link" id="mqtt-nav" onclick="showTab('mqtt')">MQTT</a></li>
            </ul>
        </div>
    </nav>
    <div class="container">
)";

    const char *_htmlFooter = R"(
    </div>
    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            var tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(function(content) {
                content.classList.remove('active');
            });

            // Remove active class from all nav links
            var navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(function(link) {
                link.classList.remove('active');
            });

            // Show selected tab content
            var selectedTab = document.getElementById(tabName + '-tab');
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // Add active class to selected nav link
            var selectedNav = document.getElementById(tabName + '-nav');
            if (selectedNav) {
                selectedNav.classList.add('active');
            }
        }

        // Network scanning functionality
        function startScan() {
            var scanBtn = document.getElementById('scanBtn');
            var scanText = document.getElementById('scanText');
            var loadingIndicator = document.getElementById('loadingIndicator');

            if (scanBtn && scanText && loadingIndicator) {
                scanText.textContent = 'Scanning...';
                loadingIndicator.style.display = 'inline-block';
                scanBtn.onclick = null; // Disable button during scan

                // Reload page after 3 seconds to show new scan results
                setTimeout(function() {
                    location.reload();
                }, 3000);
            }
        }

        // Password toggle functionality
        function togglePassword(fieldId) {
            var field = document.getElementById(fieldId);
            var checkbox = document.getElementById('show_password');
            if (field && checkbox) {
                field.type = checkbox.checked ? 'text' : 'password';
            }
        }

        // RGB color setting functionality
        function setRGBColor(index, state, color) {
            var r = 0, g = 0, b = 0;

            switch(color) {
                case 'red': r = 1; break;
                case 'green': g = 1; break;
                case 'blue': b = 1; break;
                case 'yellow': r = 1; g = 1; break;
                case 'cyan': g = 1; b = 1; break;
                case 'magenta': r = 1; b = 1; break;
                case 'white': r = 1; g = 1; b = 1; break;
                default: r = 0; g = 0; b = 0; break;
            }

            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/rgb/' + state, true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    // Update color preview
                    var preview = document.getElementById('preview_' + state + '_' + index);
                    if (preview) {
                        preview.style.backgroundColor = 'rgb(' + (r*255) + ',' + (g*255) + ',' + (b*255) + ')';
                    }
                }
            };
            xhr.send('index=' + index + '&r=' + r + '&g=' + g + '&b=' + b);
        }

        // Initialize first tab as active
        document.addEventListener('DOMContentLoaded', function() {
            showTab('dashboard');
        });
    </script>
</body>
</html>
)";

public:
    // Constructor that accepts TouchSensorManager (for compatibility with existing code)
    WebServerManager(WiFiManager *wifiManager, DeviceManager *deviceManager = nullptr, void *touchManager = nullptr, int port = 80)
        : _server(port), _wifiManager(wifiManager), _deviceManager(deviceManager), _mqttManager(nullptr), _serverRunning(false), _port(port)
    {
        // touchManager parameter is ignored for now - kept for compatibility
    }

    // Alternative constructor with MQTTManager
    WebServerManager(WiFiManager *wifiManager, DeviceManager *deviceManager, MQTTManager *mqttManager, int port = 80)
        : _server(port), _wifiManager(wifiManager), _deviceManager(deviceManager), _mqttManager(mqttManager), _serverRunning(false), _port(port)
    {
    }

    // Initialize and start the web server
    void begin()
    {
        // Main page
        _server.on("/", [this]()
                   { handleRoot(); });

        // WiFi routes
        _server.on("/wifi", [this]()
                   { handleWifi(); });
        _server.on("/connect", [this]()
                   { handleConnect(); });
        _server.on("/hidden", [this]()
                   { handleHiddenNetwork(); });
        _server.on("/submit-hidden", [this]()
                   { handleSubmitHidden(); });
        _server.on("/success", [this]()
                   { handleSuccess(); });

        // Device control routes
        _server.on("/device", [this]()
                   { handleDevice(); });
        _server.on("/switch", [this]()
                   { handleSwitch(); });
        _server.on("/rgb/off", [this]()
                   { handleRGBOff(); });
        _server.on("/rgb/on", [this]()
                   { handleRGBOn(); });

        // 404 handler
        _server.onNotFound([this]()
                           { handle404(); });

        _server.begin();
        _serverRunning = true;

        Serial.println("Web server started");
        Serial.print("Web server running on port ");
        Serial.println(_port);
    }

    // Handle client requests
    void handleClient()
    {
        if (_serverRunning)
        {
            _server.handleClient();
        }
    }

    // Check if server is running
    bool isRunning()
    {
        return _serverRunning;
    }

    // Stop the web server
    void stop()
    {
        _server.stop();
        _serverRunning = false;
        Serial.println("Web server stopped");
    }

private:
    // Handle root page with tab-based interface
    void handleRoot()
    {
        String html = _htmlHeader;

        // Dashboard Tab
        html += "<div id='dashboard-tab' class='tab-content active'>";
        html += "<h1>Dashboard</h1>";

        // WiFi Status
        html += "<h2>WiFi Connection</h2>";
        html += "<div class='info-grid'>";
        html += "<div class='info-item'>";
        html += "<div class='info-label'>Status:</div>";
        html += "<div class='info-value'>" + String(_wifiManager->isConnected() ? "Connected" : "Disconnected") + "</div>";
        html += "</div>";

        if (_wifiManager->isConnected())
        {
            String currentSSID = WiFi.SSID();
            if (currentSSID.length() == 0)
            {
                currentSSID = _wifiManager->getSSID();
            }
            IPAddress ip = WiFi.localIP();

            html += "<div class='info-item'>";
            html += "<div class='info-label'>Network:</div>";
            html += "<div class='info-value'>" + currentSSID + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>IP Address:</div>";
            html += "<div class='info-value'>" + ip.toString() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Signal Strength:</div>";
            html += "<div class='info-value'>" + String(WiFi.RSSI()) + " dBm</div>";
            html += "</div>";
        }
        html += "</div>";

        // Device Information
        if (_deviceManager)
        {
            html += "<h2>Device Information</h2>";
            html += "<div class='info-grid'>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device ID:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceID() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device Name:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceName() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device Type:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceType() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Switch Count:</div>";
            html += "<div class='info-value'>" + String(_deviceManager->getSwitchCount()) + "</div>";
            html += "</div>";
            html += "</div>";

            // Switch Status Overview
            html += "<h2>Switch Status</h2>";
            html += "<div class='info-grid'>";
            for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
            {
                bool state = _deviceManager->getSwitchState(i);
                html += "<div class='info-item'>";
                html += "<div class='info-label'>Switch " + String(i + 1) + ":</div>";
                html += "<div class='info-value'>";
                html += "<span class='switch-status " + String(state ? "on" : "off") + "'>" + String(state ? "ON" : "OFF") + "</span>";
                html += "</div>";
                html += "</div>";
            }
            html += "</div>";
        }
        html += "</div>"; // Close dashboard tab

        // WiFi Tab
        html += "<div id='wifi-tab' class='tab-content'>";
        html += "<h1>WiFi Configuration</h1>";

        // Add a scan button at the top with loading indicator
        html += "<div class='scan-button'>";
        html += "<div class='network-item scan-item' id='scanBtn' onclick='startScan()'>";
        html += "<span id='scanText'>Scan for Networks</span>";
        html += "<span id='loadingIndicator' class='loading-spinner' style='display:none; float: right;'></span>";
        html += "</div>";
        html += "</div>";

        // Current network
        if (_wifiManager->isConnected())
        {
            String currentSSID = WiFi.SSID();
            if (currentSSID.length() == 0)
            {
                currentSSID = _wifiManager->getSSID();
            }

            html += "<div class='section-header'>Current Network</div>";
            html += "<div class='network-item current-network'>";
            html += "<div class='network-name'>" + currentSSID + "</div>";
            html += "<span class='signal-strength'>" + String(WiFi.RSSI()) + " dBm</span>";
            html += "</div>";
        }

        // Available networks
        html += "<div class='section-header'>Available Networks</div>";
        html += "<ul class='network-list'>";

        int n = WiFi.scanNetworks();
        if (n == 0)
        {
            html += "<div class='network-item'>";
            html += "<div class='network-name'>No networks found</div>";
            html += "</div>";
        }
        else
        {
            for (int i = 0; i < n; ++i)
            {
                String ssid = WiFi.SSID(i);
                int32_t rssi = WiFi.RSSI(i);
                String encType = (WiFi.encryptionType(i) == ENC_TYPE_NONE) ? "Open" : "Secured";

                html += "<div class='network-item' onclick='window.location=\"/connect?ssid=" + ssid + "\"'>";
                html += "<div class='network-name'>" + ssid + "</div>";
                html += "<span class='signal-strength'>" + String(rssi) + " dBm (" + encType + ")</span>";
                html += "</div>";
            }
        }

        html += "</ul>";

        html += "<div class='section-header'>Manual Connection</div>";
        html += "<div class='network-item hidden-network-item' onclick='window.location=\"/hidden\"'>";
        html += "<span class='plus-sign'>+</span>Add Hidden Network";
        html += "</div>";
        html += "</div>"; // Close wifi tab

        // Switch Control Tab
        html += "<div id='switches-tab' class='tab-content'>";
        html += "<h1>Switch Control</h1>";

        if (_deviceManager)
        {
            // Switch controls
            html += "<div class='switch-grid'>";
            for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
            {
                bool state = _deviceManager->getSwitchState(i);

                bool rOff, gOff, bOff, rOn, gOn, bOn;
                _deviceManager->getRGBOff(i, rOff, gOff, bOff);
                _deviceManager->getRGBOn(i, rOn, gOn, bOn);

                html += "<div class='switch-card'>";
                html += "<div class='switch-header'>";
                html += "<div class='switch-title'>Switch " + String(i + 1) + "</div>";
                html += "<span class='switch-state " + String(state ? "on" : "off") + "'>" + String(state ? "ON" : "OFF") + "</span>";
                html += "</div>";

                // Toggle switch
                html += "<div class='toggle-container'>";
                html += "<span>Toggle Switch:</span>";
                html += "<div class='toggle-switch " + String(state ? "on" : "off") + "' onclick='toggleSwitch(" + String(i) + ")'></div>";
                html += "</div>";

                // Color controls
                html += "<div class='color-controls'>";

                // OFF state color
                html += "<div class='color-group'>";
                html += "<h4>OFF State Color</h4>";
                html += "<div class='color-selector'>";
                html += "<select class='color-dropdown' onchange='setRGBColor(" + String(i) + ", \"off\", this.value)'>";
                html += "<option value='off'" + String((!rOff && !gOff && !bOff) ? " selected" : "") + ">Off</option>";
                html += "<option value='red'" + String((rOff && !gOff && !bOff) ? " selected" : "") + ">Red</option>";
                html += "<option value='green'" + String((!rOff && gOff && !bOff) ? " selected" : "") + ">Green</option>";
                html += "<option value='blue'" + String((!rOff && !gOff && bOff) ? " selected" : "") + ">Blue</option>";
                html += "<option value='yellow'" + String((rOff && gOff && !bOff) ? " selected" : "") + ">Yellow</option>";
                html += "<option value='cyan'" + String((!rOff && gOff && bOff) ? " selected" : "") + ">Cyan</option>";
                html += "<option value='magenta'" + String((rOff && !gOff && bOff) ? " selected" : "") + ">Magenta</option>";
                html += "<option value='white'" + String((rOff && gOff && bOff) ? " selected" : "") + ">White</option>";
                html += "</select>";
                html += "<div class='color-preview' id='preview_off_" + String(i) + "' style='background-color: rgb(" + String(rOff ? 255 : 0) + "," + String(gOff ? 255 : 0) + "," + String(bOff ? 255 : 0) + ");'></div>";
                html += "</div>";
                html += "</div>";

                // ON state color
                html += "<div class='color-group'>";
                html += "<h4>ON State Color</h4>";
                html += "<div class='color-selector'>";
                html += "<select class='color-dropdown' onchange='setRGBColor(" + String(i) + ", \"on\", this.value)'>";
                html += "<option value='off'" + String((!rOn && !gOn && !bOn) ? " selected" : "") + ">Off</option>";
                html += "<option value='red'" + String((rOn && !gOn && !bOn) ? " selected" : "") + ">Red</option>";
                html += "<option value='green'" + String((!rOn && gOn && !bOn) ? " selected" : "") + ">Green</option>";
                html += "<option value='blue'" + String((!rOn && !gOn && bOn) ? " selected" : "") + ">Blue</option>";
                html += "<option value='yellow'" + String((rOn && gOn && !bOn) ? " selected" : "") + ">Yellow</option>";
                html += "<option value='cyan'" + String((!rOn && gOn && bOn) ? " selected" : "") + ">Cyan</option>";
                html += "<option value='magenta'" + String((rOn && !gOn && bOn) ? " selected" : "") + ">Magenta</option>";
                html += "<option value='white'" + String((rOn && gOn && bOn) ? " selected" : "") + ">White</option>";
                html += "</select>";
                html += "<div class='color-preview' id='preview_on_" + String(i) + "' style='background-color: rgb(" + String(rOn ? 255 : 0) + "," + String(gOn ? 255 : 0) + "," + String(bOn ? 255 : 0) + ");'></div>";
                html += "</div>";
                html += "</div>";

                html += "</div>"; // Close color-controls
                html += "</div>"; // Close switch-card
            }
            html += "</div>"; // Close switch-grid
        }
        else
        {
            html += "<p>Device manager not available.</p>";
        }
        html += "</div>"; // Close switches tab

        // MQTT Tab
        html += "<div id='mqtt-tab' class='tab-content'>";
        html += "<h1>MQTT Status</h1>";

        // MQTT Connection Status
        html += "<h2>Connection Status</h2>";
        html += "<div class='info-grid'>";
        html += "<div class='info-item'>";
        html += "<div class='info-label'>Status:</div>";
        if (_mqttManager)
        {
            html += "<div class='info-value'>" + String(_mqttManager->isConnected() ? "Connected" : "Disconnected") + "</div>";
        }
        else
        {
            html += "<div class='info-value'>Information not available</div>";
        }
        html += "</div>";
        html += "<div class='info-item'>";
        html += "<div class='info-label'>Note:</div>";
        html += "<div class='info-value'>MQTT status will be implemented in a future update</div>";
        html += "</div>";
        html += "</div>";

        // MQTT Topics Information
        if (_deviceManager)
        {
            html += "<h2>MQTT Topics</h2>";
            html += "<div class='info-grid'>";

            String deviceId = _deviceManager->getDeviceID();

            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device ID:</div>";
            html += "<div class='info-value'>" + deviceId + "</div>";
            html += "</div>";

            html += "<div class='info-item'>";
            html += "<div class='info-label'>Status Topic:</div>";
            html += "<div class='info-value'>home/switches/" + deviceId + "/available</div>";
            html += "</div>";

            html += "</div>";

            html += "<h2>Switch Control Topics</h2>";
            html += "<div class='info-grid'>";
            for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
            {
                html += "<div class='info-item'>";
                html += "<div class='info-label'>Switch " + String(i + 1) + " Set:</div>";
                html += "<div class='info-value'>home/switches/" + deviceId + "/" + String(i) + "/set</div>";
                html += "</div>";
                html += "<div class='info-item'>";
                html += "<div class='info-label'>Switch " + String(i + 1) + " State:</div>";
                html += "<div class='info-value'>home/switches/" + deviceId + "/" + String(i) + "/state</div>";
                html += "</div>";
            }
            html += "</div>";
        }
        html += "</div>"; // Close mqtt tab

        // Add JavaScript for toggle functionality
        html += "<script>";
        html += "function toggleSwitch(index) {";
        html += "  var xhr = new XMLHttpRequest();";
        html += "  xhr.open('POST', '/switch', true);";
        html += "  xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');";
        html += "  xhr.onreadystatechange = function() {";
        html += "    if (xhr.readyState === 4 && xhr.status === 200) {";
        html += "      location.reload();";
        html += "    }";
        html += "  };";
        html += "  var currentState = document.querySelector('.switch-card:nth-child(' + (index + 1) + ') .switch-state').classList.contains('on');";
        html += "  xhr.send('index=' + index + '&state=' + (currentState ? '0' : '1'));";
        html += "}";
        html += "</script>";

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle WiFi configuration page
    void handleWifi()
    {
        // Redirect to main page with WiFi tab active
        _server.sendHeader("Location", "/#wifi", true);
        _server.send(302, "text/plain", "");
    }

    // Handle connect page - show password form for selected network
    void handleConnect()
    {
        String ssid = _server.arg("ssid");

        String html = _htmlHeader;

        html += "<div id='dashboard-tab' class='tab-content active'>";
        html += "<h1>Connect to Network</h1>";
        html += "<h2>" + ssid + "</h2>";

        html += "<form method='post' action='/submit-hidden'>";
        html += "<input type='hidden' name='ssid' value='" + ssid + "'>";

        html += "<div class='form-group'>";
        html += "<label for='password'>Network Password:</label>";
        html += "<input type='password' id='password' name='password' required placeholder='Enter network password'>";
        html += "</div>";
        html += "<div class='password-toggle'>";
        html += "<input type='checkbox' id='show_password' onchange='togglePassword(\"password\")'>";
        html += "<label for='show_password'>Show password</label>";
        html += "</div>";

        html += "<div class='button-container'>";
        html += "<button type='button' class='btn-back' onclick='window.location=\"/\"'>Cancel</button>";
        html += "<button type='submit' class='btn-primary'>Connect</button>";
        html += "</div>";
        html += "</form>";
        html += "</div>";
        html += _htmlFooter;

        _server.send(200, "text/html", html);
    }

    // Handle hidden network page - show form for hidden network
    void handleHiddenNetwork()
    {
        String html = _htmlHeader;

        html += "<div id='dashboard-tab' class='tab-content active'>";
        html += "<h1>Hidden Network</h1>";
        html += "<h2>Add Hidden Network</h2>";

        html += "<form method='post' action='/submit-hidden'>";

        html += "<div class='form-group'>";
        html += "<label for='ssid'>Network Name (SSID):</label>";
        html += "<input type='text' id='ssid' name='ssid' required placeholder='Enter network name'>";
        html += "</div>";

        html += "<div class='form-group'>";
        html += "<label for='password'>Network Password:</label>";
        html += "<input type='password' id='password' name='password' required placeholder='Enter network password'>";
        html += "</div>";
        html += "<div class='password-toggle'>";
        html += "<input type='checkbox' id='show_password' onchange='togglePassword(\"password\")'>";
        html += "<label for='show_password'>Show password</label>";
        html += "</div>";

        html += "<div class='button-container'>";
        html += "<button type='button' class='btn-back' onclick='window.location=\"/\"'>Cancel</button>";
        html += "<button type='submit' class='btn-primary'>Connect</button>";
        html += "</div>";
        html += "</form>";
        html += "</div>";
        html += _htmlFooter;

        _server.send(200, "text/html", html);
    }

    // Handle WiFi connection submission
    void handleSubmitHidden()
    {
        String ssid = _server.arg("ssid");
        String password = _server.arg("password");

        if (ssid.length() == 0)
        {
            _server.send(400, "text/plain", "SSID is required");
            return;
        }

        // Attempt to connect
        bool connected = _wifiManager->connect(ssid, password);

        // Save credentials if connection was successful
        if (connected)
        {
            _wifiManager->saveCredentials(ssid, password);
        }

        if (connected)
        {
            // Redirect to main page on success
            _server.sendHeader("Location", "/", true);
            _server.send(302, "text/plain", "");
        }
        else
        {
            // Redirect to error page on failure
            _server.sendHeader("Location", "/success", true);
            _server.send(302, "text/plain", "");
        }
    }

    // Handle success page - now only used for connection failures
    void handleSuccess()
    {
        String html = _htmlHeader;

        html += "<div id='dashboard-tab' class='tab-content active'>";
        html += "<h1>Connection Status</h1>";

        // We only show the error message now since successful connections go directly to the main page
        html += "<div class='status error'>";
        html += "<h2>Connection Failed</h2>";
        html += "<p>The device could not connect to the network after multiple attempts.</p>";
        html += "<p>Please check your password and make sure the network is in range.</p>";
        html += "<p>You can try again by selecting the network from the list.</p>";
        html += "</div>";

        html += "<div class='button-container'>";
        html += "<button type='button' class='btn-secondary' onclick='window.location=\"/\"'>Try Again</button>";
        html += "<button type='button' class='btn-back' onclick='window.location=\"/\"'>Back to Home</button>";
        html += "</div>";
        html += "</div>";
        html += _htmlFooter;

        _server.send(200, "text/html", html);
    }

    // Handle device control page (redirect to main page)
    void handleDevice()
    {
        // Redirect to main page with switches tab active
        _server.sendHeader("Location", "/#switches", true);
        _server.send(302, "text/plain", "");
    }

    // Handle switch control
    void handleSwitch()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("state"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool state = _server.arg("state") == "1";

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setSwitchState(index, state);

        // Return success
        _server.send(200, "text/plain", "OK");
    }

    // Handle RGB OFF color control
    void handleRGBOff()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("r") || !_server.hasArg("g") || !_server.hasArg("b"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool r = _server.arg("r") == "1";
        bool g = _server.arg("g") == "1";
        bool b = _server.arg("b") == "1";

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setRGBOff(index, r, g, b);
        _server.send(200, "text/plain", "RGB OFF color set");
    }

    // Handle RGB ON color control
    void handleRGBOn()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("r") || !_server.hasArg("g") || !_server.hasArg("b"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool r = _server.arg("r") == "1";
        bool g = _server.arg("g") == "1";
        bool b = _server.arg("b") == "1";

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setRGBOn(index, r, g, b);
        _server.send(200, "text/plain", "RGB ON color set");
    }

    // Handle 404 errors
    void handle404()
    {
        String html = _htmlHeader;

        html += "<div id='dashboard-tab' class='tab-content active'>";
        html += "<h1>Page Not Found</h1>";
        html += "<p>The requested page was not found.</p>";
        html += "<button onclick='window.location=\"/\"'>Back to Home</button>";
        html += "</div>";
        html += _htmlFooter;

        _server.send(404, "text/html", html);
    }
};

#endif // WEB_SERVER_MANAGER_H
