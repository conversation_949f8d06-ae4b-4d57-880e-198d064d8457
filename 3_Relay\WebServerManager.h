#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include "WiFiManager.h"
#include "DeviceManager.h"
#include "TouchSensorManager.h"

class WebServerManager
{
private:
    ESP8266WebServer _server;
    WiFiManager *_wifiManager;
    DeviceManager *_deviceManager;
    TouchSensorManager *_touchManager;
    bool _serverRunning;
    int _port;

    // HTML templates
    const char *_htmlHeader = R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3-Relay Switch Control</title>
    <script>
    function setRGBColor(switchIndex, state, color) {
        var r = 0, g = 0, b = 0;
        switch(color) {
            case 'red': r = 1; break;
            case 'green': g = 1; break;
            case 'blue': b = 1; break;
            case 'yellow': r = 1; g = 1; break;
            case 'cyan': g = 1; b = 1; break;
            case 'magenta': r = 1; b = 1; break;
            case 'white': r = 1; g = 1; b = 1; break;
            case 'off': break;
        }

        var xhr = new XMLHttpRequest();
        xhr.open('POST', '/rgb/' + state, true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.send('index=' + switchIndex + '&r=' + r + '&g=' + g + '&b=' + b);

        // Update color preview
        var preview = document.getElementById('preview_' + state + '_' + switchIndex);
        if (preview) {
            preview.style.backgroundColor = 'rgb(' + (r*255) + ',' + (g*255) + ',' + (b*255) + ')';
        }
    }
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }

        /* Navigation Bar */
        .navbar {
            background-color: #2d2d2d;
            padding: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            border-bottom: 2px solid #666;
        }
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
        }
        .nav-brand {
            padding: 15px 20px;
            font-size: 1.2em;
            font-weight: bold;
            color: #ccc;
            text-decoration: none;
        }
        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            flex: 1;
        }
        .nav-item {
            margin: 0;
        }
        .nav-link {
            display: block;
            padding: 15px 20px;
            color: #e0e0e0;
            text-decoration: none;
            transition: background-color 0.3s, color 0.3s;
            border-radius: 0;
        }
        .nav-link:hover {
            background-color: #3d3d3d;
            color: #fff;
        }
        .nav-link.active {
            background-color: #555;
            color: white;
        }

        /* Main Content */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .content-section {
            background-color: #2d2d2d;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }

        h1 {
            color: #ccc;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2em;
        }
        h2 {
            color: #bbb;
            border-bottom: 2px solid #666;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        h3 {
            color: #bbb;
            margin-bottom: 15px;
        }

        /* Info Grid */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-item {
            background-color: #3d3d3d;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #666;
        }
        .info-label {
            font-weight: bold;
            color: #bbb;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        .info-value {
            color: #e0e0e0;
            font-size: 1.1em;
            word-break: break-all;
        }

        /* Switch Controls */
        .switch-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .switch-card {
            background-color: #3d3d3d;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #555;
        }
        .switch-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .switch-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #bbb;
        }
        .switch-state {
            font-weight: bold;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .switch-state.on {
            background-color: #666;
            color: white;
        }
        .switch-state.off {
            background-color: #444;
            color: white;
        }
        .switch-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .switch-status.on {
            background-color: #666;
            color: white;
        }
        .switch-status.off {
            background-color: #444;
            color: white;
        }
        .switch-container {
            background-color: #3d3d3d;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }

        /* Toggle Switch */
        .toggle-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background-color: #555;
            border-radius: 15px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .toggle-switch.on {
            background-color: #777;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            background-color: white;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        .toggle-switch.on::after {
            transform: translateX(30px);
        }

        /* Color Controls */
        .color-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .color-group {
            background-color: #4a4a4a;
            padding: 20px;
            border-radius: 12px;
        }
        .color-group h4 {
            margin: 0 0 15px 0;
            color: #bbb;
            font-size: 1em;
        }
        .color-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .color-dropdown {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #666;
            border-radius: 8px;
            background-color: #3d3d3d;
            color: #e0e0e0;
            font-size: 14px;
        }
        .color-preview {
            width: 30px;
            height: 30px;
            border: 2px solid #666;
            border-radius: 8px;
            background-color: #000;
        }

        /* Buttons */
        .button-container {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        button, .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #666;
            color: white;
        }
        .btn-primary:hover {
            background-color: #777;
        }
        .btn-secondary {
            background-color: #555;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #666;
        }
        .nav-button {
            background-color: #555;
            color: white;
        }
        .nav-button:hover {
            background-color: #666;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-menu {
                flex-direction: column;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: #2d2d2d;
                display: none;
            }
            .nav-menu.active {
                display: flex;
            }
            .container {
                padding: 10px;
            }
            .info-grid {
                grid-template-columns: 1fr;
            }
            .switch-grid {
                grid-template-columns: 1fr;
            }
            .color-controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="nav-brand">3-Relay Switch</a>
            <ul class="nav-menu">
                <li class="nav-item"><a href="/" class="nav-link">Dashboard</a></li>
                <li class="nav-item"><a href="/wifi" class="nav-link">WiFi</a></li>
                <li class="nav-item"><a href="/switches" class="nav-link">Switch Control</a></li>
                <li class="nav-item"><a href="/mqtt" class="nav-link">MQTT</a></li>
            </ul>
        </div>
    </nav>
    <div class="container">
)";

    const char *_htmlFooter = R"(
    </div>
    <script>
        // Set active navigation link
        function setActiveNav(path) {
            var links = document.querySelectorAll('.nav-link');
            links.forEach(function(link) {
                link.classList.remove('active');
                if (link.getAttribute('href') === path) {
                    link.classList.add('active');
                }
            });
        }

        // Set active nav based on current path
        var currentPath = window.location.pathname;
        setActiveNav(currentPath);
    </script>
</body>
</html>
)";

public:
    WebServerManager(WiFiManager *wifiManager, DeviceManager *deviceManager = nullptr, TouchSensorManager *touchManager = nullptr, int port = 80)
        : _server(port), _wifiManager(wifiManager), _deviceManager(deviceManager), _touchManager(touchManager), _serverRunning(false), _port(port)
    {
    }

    // Initialize and start the web server
    void begin()
    {
        // Main page
        _server.on("/", [this]()
                   { handleRoot(); });

        // Device control routes
        _server.on("/device", [this]()
                   { handleDevice(); });
        _server.on("/switches", [this]()
                   { handleSwitches(); });
        _server.on("/switch", [this]()
                   { handleSwitch(); });
        _server.on("/rgb/off", [this]()
                   { handleRGBOff(); });
        _server.on("/rgb/on", [this]()
                   { handleRGBOn(); });

        // WiFi and MQTT routes
        _server.on("/wifi", [this]()
                   { handleWiFi(); });
        _server.on("/mqtt", [this]()
                   { handleMqtt(); });

        // 404 handler
        _server.onNotFound([this]()
                           { handle404(); });

        _server.begin();
        _serverRunning = true;

        Serial.println("3-Relay Web server started");
        Serial.print("3-Relay Web server running on port ");
        Serial.println(_port);
    }

    // Handle client requests
    void handleClient()
    {
        if (_serverRunning)
        {
            _server.handleClient();
        }
    }

    // Check if server is running
    bool isRunning()
    {
        return _serverRunning;
    }

    // Stop the web server
    void stop()
    {
        _server.stop();
        _serverRunning = false;
        Serial.println("3-Relay Web server stopped");
    }

private:
    // Handle root page
    void handleRoot()
    {
        String html = _htmlHeader;

        html += "<div class='content-section'>";
        html += "<h1>3-Relay Dashboard</h1>";

        // WiFi Status
        html += "<h2>WiFi Connection</h2>";
        html += "<div class='info-grid'>";
        html += "<div class='info-item'>";
        html += "<div class='info-label'>Status:</div>";
        html += "<div class='info-value'>" + String(_wifiManager->isConnected() ? "Connected" : "Disconnected") + "</div>";
        html += "</div>";

        if (_wifiManager->isConnected())
        {
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Network:</div>";
            html += "<div class='info-value'>" + _wifiManager->getSSID() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>IP Address:</div>";
            html += "<div class='info-value'>" + _wifiManager->getIP().toString() + "</div>";
            html += "</div>";
        }
        html += "</div>";

        // Device Information
        if (_deviceManager)
        {
            html += "<h2>Device Information</h2>";
            html += "<div class='info-grid'>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device ID:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceID() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device Name:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceName() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device Type:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceType() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Switch Count:</div>";
            html += "<div class='info-value'>" + String(_deviceManager->getSwitchCount()) + "</div>";
            html += "</div>";
            html += "</div>";

            // Switch Status
            html += "<h2>Switch Status</h2>";
            html += "<div class='info-grid'>";
            for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
            {
                bool state = _deviceManager->getSwitchState(i);
                html += "<div class='info-item'>";
                html += "<div class='info-label'>Switch " + String(i + 1) + ":</div>";
                html += "<div class='info-value'>";
                html += "<span class='switch-status " + String(state ? "on" : "off") + "'>" + String(state ? "ON" : "OFF") + "</span>";
                html += "</div>";
                html += "</div>";
            }
            html += "</div>";
        }
        html += "</div>";

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle device control page (simplified for 3-Relay)
    void handleDevice()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        String html = _htmlHeader;
        html += "<h1>3-Relay Device Control</h1>";

        // Back button
        html += "<div class='button-container'>";
        html += "<button type='button' class='nav-button' onclick='window.location=\"/\"'>← Back to Home</button>";
        html += "</div>";

        // Switch controls (simplified for 3 switches)
        html += "<h2>3-Relay Switch Controls</h2>";
        for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
        {
            bool state = _deviceManager->getSwitchState(i);
            String switchClass = state ? "on" : "off";

            html += "<div class='switch-container'>";
            html += "<p><strong>Switch " + String(i + 1) + ":</strong> ";
            html += "<span class='switch-state " + switchClass + "'>" + (state ? "ON" : "OFF") + "</span></p>";
            if (_touchManager)
            {
                html += "<p><strong>Touch Pin:</strong> " + String(_touchManager->getTouchPin(i)) + "</p>";
            }

            html += "<div class='button-container'>";
            html += "<button type='button' onclick='window.location=\"/switch?index=" + String(i) + "&state=" + (state ? "0" : "1") + "\"'>";
            html += (state ? "Turn OFF" : "Turn ON");
            html += "</button>";
            html += "</div>";
            html += "</div>";
        }

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle switches control page
    void handleSwitches()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        String html = _htmlHeader;

        html += "<div class='content-section'>";
        html += "<h1>3-Relay Switch Control</h1>";

        // Switch controls (simplified for 3-relay)
        html += "<div class='switch-grid'>";
        for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
        {
            bool state = _deviceManager->getSwitchState(i);

            html += "<div class='switch-card'>";
            html += "<div class='switch-header'>";
            html += "<div class='switch-title'>Switch " + String(i + 1) + "</div>";
            html += "<span class='switch-state " + String(state ? "on" : "off") + "'>" + String(state ? "ON" : "OFF") + "</span>";
            html += "</div>";

            // Toggle switch
            html += "<div class='toggle-container'>";
            html += "<span>Toggle Switch:</span>";
            html += "<div class='toggle-switch " + String(state ? "on" : "off") + "' onclick='toggleSwitch(" + String(i) + ")'></div>";
            html += "</div>";

            // Touch sensor info if available
            if (_touchManager)
            {
                html += "<p><strong>Touch Pin:</strong> " + String(_touchManager->getTouchPin(i)) + "</p>";
            }

            html += "</div>"; // Close switch-card
        }
        html += "</div>"; // Close switch-grid
        html += "</div>"; // Close content-section

        // Add JavaScript for toggle functionality
        html += "<script>";
        html += "function toggleSwitch(index) {";
        html += "  var xhr = new XMLHttpRequest();";
        html += "  xhr.open('POST', '/switch', true);";
        html += "  xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');";
        html += "  xhr.onreadystatechange = function() {";
        html += "    if (xhr.readyState === 4 && xhr.status === 302) {";
        html += "      window.location.reload();";
        html += "    }";
        html += "  };";
        html += "  var currentState = document.querySelector('.switch-card:nth-child(' + (index + 1) + ') .switch-state').classList.contains('on');";
        html += "  xhr.send('index=' + index + '&state=' + (currentState ? '0' : '1'));";
        html += "}";
        html += "</script>";

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle WiFi configuration page (simplified)
    void handleWiFi()
    {
        String html = _htmlHeader;

        html += "<div class='content-section'>";
        html += "<h1>3-Relay WiFi Configuration</h1>";
        html += "<p>WiFi management interface would go here...</p>";
        html += "<p>This is a simplified version for the 3-relay variant.</p>";
        html += "</div>";

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle MQTT status page
    void handleMqtt()
    {
        String html = _htmlHeader;

        html += "<div class='content-section'>";
        html += "<h1>3-Relay MQTT Status</h1>";

        // MQTT Connection Status
        html += "<h2>Connection Status</h2>";
        html += "<div class='info-grid'>";
        html += "<div class='info-item'>";
        html += "<div class='info-label'>Status:</div>";
        html += "<div class='info-value'>🔴 Information not available</div>";
        html += "</div>";
        html += "<div class='info-item'>";
        html += "<div class='info-label'>Note:</div>";
        html += "<div class='info-value'>MQTT status will be implemented in a future update</div>";
        html += "</div>";
        html += "</div>";

        // MQTT Topics Information
        if (_deviceManager)
        {
            html += "<h2>MQTT Topics</h2>";
            html += "<div class='info-grid'>";

            String deviceId = _deviceManager->getDeviceID();

            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device ID:</div>";
            html += "<div class='info-value'>" + deviceId + "</div>";
            html += "</div>";

            html += "<div class='info-item'>";
            html += "<div class='info-label'>Status Topic:</div>";
            html += "<div class='info-value'>home/switches/" + deviceId + "/available</div>";
            html += "</div>";

            html += "</div>";

            html += "<h2>Switch Control Topics</h2>";
            html += "<div class='info-grid'>";
            for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
            {
                html += "<div class='info-item'>";
                html += "<div class='info-label'>Switch " + String(i + 1) + " Set:</div>";
                html += "<div class='info-value'>home/switches/" + deviceId + "/" + String(i) + "/set</div>";
                html += "</div>";
                html += "<div class='info-item'>";
                html += "<div class='info-label'>Switch " + String(i + 1) + " State:</div>";
                html += "<div class='info-value'>home/switches/" + deviceId + "/" + String(i) + "/state</div>";
                html += "</div>";
            }
            html += "</div>";
        }
        html += "</div>";

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle switch control
    void handleSwitch()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("state"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool state = _server.arg("state") == "1";

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setSwitchState(index, state);

        // Redirect back to switches page
        _server.sendHeader("Location", "/switches", true);
        _server.send(302, "text/plain", "");
    }

    // Handle RGB OFF color control
    void handleRGBOff()
    {
        _server.send(200, "text/plain", "3-Relay RGB OFF control (simplified)");
    }

    // Handle RGB ON color control
    void handleRGBOn()
    {
        _server.send(200, "text/plain", "3-Relay RGB ON control (simplified)");
    }

    // Handle 404 errors
    void handle404()
    {
        String html = _htmlHeader;
        html += "<h1>3-Relay Page Not Found</h1>";
        html += "<p>The requested page was not found.</p>";
        html += "<button onclick='window.location=\"/\"'>Back to Home</button>";
        html += _htmlFooter;
        _server.send(404, "text/html", html);
    }
};

#endif // WEB_SERVER_MANAGER_H
